// Backend API base URL
const BASE_URL = 'http://localhost:3000';

// Mock data for testing - Updated to match backend format
const mockStations = [
  {
    id: '78012206-1aa1-11e9-a80b-52543be04c81',
    name: 'BBC Radio 1',
    streamUrl: 'http://stream.live.vc.bbcmedia.co.uk/bbc_radio_one',
    homepage: 'https://www.bbc.co.uk/radio1',
    favicon: 'https://www.bbc.co.uk/favicon.ico',
    country: 'United Kingdom',
    countryCode: 'GB',
    language: 'english',
    votes: 1250,
    codec: 'MP3',
    bitrate: 128,
    tags: 'pop,music,uk',
    genre: 'pop',
    isWorking: true,
    lastChecked: '2025-05-28 12:00:00',
    clickCount: 5000
  },
  {
    id: '34e22e2e-bf26-4314-b9b5-93fb33d25e72',
    name: 'TRT Radyo 1',
    streamUrl: 'https://radio-trtradyo1.live.trt.com.tr/master_720.m3u8',
    homepage: 'https://www.trt.net.tr',
    favicon: 'https://www.trt.net.tr/favicon.ico',
    country: 'Turkey',
    countryCode: 'TR',
    language: 'turkish',
    votes: 890,
    codec: 'MP3',
    bitrate: 128,
    tags: 'news,talk,turkish',
    genre: 'talk',
    isWorking: true,
    lastChecked: '2025-05-28 12:00:00',
    clickCount: 3200
  },
  {
    id: '12345678-1234-1234-1234-123456789abc',
    name: 'NPR News',
    streamUrl: 'https://npr-ice.streamguys1.com/live.mp3',
    homepage: 'https://www.npr.org',
    favicon: 'https://www.npr.org/favicon.ico',
    country: 'United States',
    countryCode: 'US',
    language: 'english',
    votes: 2100,
    codec: 'MP3',
    bitrate: 128,
    tags: 'news,talk,usa',
    genre: 'news',
    isWorking: true,
    lastChecked: '2025-05-28 12:00:00',
    clickCount: 8500
  }
];

// Transform backend data to app format
const transformStationData = (backendStation) => {
  return {
    id: backendStation.id,
    stationuuid: backendStation.id, // For compatibility with existing code
    name: backendStation.name,
    country: backendStation.country,
    url: backendStation.streamUrl, // Map streamUrl to url for compatibility
    streamUrl: backendStation.streamUrl,
    homepage: backendStation.homepage,
    favicon: backendStation.favicon,
    countryCode: backendStation.countryCode,
    language: backendStation.language,
    votes: backendStation.votes,
    codec: backendStation.codec,
    bitrate: backendStation.bitrate,
    tags: backendStation.tags,
    genre: backendStation.genre,
    isWorking: backendStation.isWorking,
    lastChecked: backendStation.lastChecked,
    clickcount: backendStation.clickCount, // Map clickCount to clickcount for compatibility
    clickCount: backendStation.clickCount
  };
};

// Fetch wrapper function with detailed logging
const apiRequest = async (endpoint, options = {}) => {
  const url = `${BASE_URL}${endpoint}`;
  const config = {
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': 'RadioBrowserApp/1.0.0',
    },
    ...options,
  };

  console.log('🔗 API Request:', {
    url,
    method: config.method || 'GET',
    timestamp: new Date().toISOString()
  });

  try {
    const startTime = Date.now();
    const response = await fetch(url, config);
    const endTime = Date.now();

    console.log('📡 API Response:', {
      url,
      status: response.status,
      statusText: response.statusText,
      duration: `${endTime - startTime}ms`,
      timestamp: new Date().toISOString()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
    }

    const data = await response.json();
    console.log('✅ API Data received:', {
      url,
      dataLength: Array.isArray(data) ? data.length : 'object',
      firstItem: Array.isArray(data) && data.length > 0 ? data[0] : null,
      timestamp: new Date().toISOString()
    });

    return data;
  } catch (error) {
    console.error('❌ API Request Error:', {
      url,
      error: error.message,
      timestamp: new Date().toISOString()
    });
    throw error;
  }
};

// Backend API Functions - GET & POST Endpoints
export const radioApi = {

  // GET Endpoints

  // 1. Get top/popular stations
  getTopStations: async (limit = 20) => {
    console.log('🎯 Getting Top Stations...');
    try {
      // Try real API first, fallback to mock data
      try {
        const response = await apiRequest(`/api/stations/top?limit=${limit}`);
        console.log('🎵 Top Stations API Response:', response);

        // Handle new backend response format
        if (response && response.success && response.data) {
          const transformedStations = response.data.map(transformStationData);
          console.log('🎵 Top Stations Success (API):', transformedStations.length, 'stations received');
          return transformedStations;
        } else {
          throw new Error('Invalid response format from API');
        }
      } catch (apiError) {
        console.log('⚠️ API failed, using mock data:', apiError.message);
        const mockData = mockStations.slice(0, limit).map(transformStationData);
        console.log('🎵 Top Stations Success (Mock):', mockData.length, 'stations received');
        return mockData;
      }
    } catch (error) {
      console.error('❌ Top Stations Failed:', error.message);
      throw error;
    }
  },

  // 2. Search stations by name
  searchStations: async (query, limit = 15) => {
    console.log('🔍 Searching Stations for:', query);
    try {
      // Try real API first, fallback to mock data
      try {
        const response = await apiRequest(`/api/stations/search/${encodeURIComponent(query)}?limit=${limit}`);
        console.log('🎵 Search API Response:', response);

        // Handle new backend response format
        if (response && response.success && response.data) {
          const transformedStations = response.data.map(transformStationData);
          console.log('🎵 Search Success (API):', transformedStations.length, 'stations found for:', query);
          return transformedStations;
        } else {
          throw new Error('Invalid response format from API');
        }
      } catch (apiError) {
        console.log('⚠️ API failed, using mock search:', apiError.message);
        const mockData = mockStations.filter(station =>
          station.name.toLowerCase().includes(query.toLowerCase()) ||
          station.tags.toLowerCase().includes(query.toLowerCase())
        ).slice(0, limit).map(transformStationData);
        console.log('🎵 Search Success (Mock):', mockData.length, 'stations found for:', query);
        return mockData;
      }
    } catch (error) {
      console.error('❌ Search Failed for:', query, error.message);
      throw error;
    }
  },

  // 3. Get stations by country
  getStationsByCountry: async (country, limit = 10) => {
    console.log('🌍 Getting Stations for Country:', country);
    try {
      // Try real API first, fallback to mock data
      try {
        const response = await apiRequest(`/api/stations/country/${encodeURIComponent(country)}?limit=${limit}`);
        console.log('🎵 Country Stations API Response:', response);

        // Handle new backend response format
        if (response && response.success && response.data) {
          const transformedStations = response.data.map(transformStationData);
          console.log('🎵 Country Stations Success (API):', transformedStations.length, 'stations for:', country);
          return transformedStations;
        } else {
          throw new Error('Invalid response format from API');
        }
      } catch (apiError) {
        console.log('⚠️ API failed, using mock country data:', apiError.message);
        const mockData = mockStations.filter(station =>
          station.country.toLowerCase().includes(country.toLowerCase())
        ).slice(0, limit).map(transformStationData);
        console.log('🎵 Country Stations Success (Mock):', mockData.length, 'stations for:', country);
        return mockData;
      }
    } catch (error) {
      console.error('❌ Country Stations Failed for:', country, error.message);
      throw error;
    }
  },

  // POST Endpoints

  // 4. Add station to favorites
  addToFavorites: async (stationId, userId = 'default-user', stationName) => {
    console.log('❤️ Adding to Favorites:', { stationId, userId, stationName });
    try {
      // Try real API first, fallback to mock response
      try {
        const response = await apiRequest('/api/stations/favorite', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            stationId,
            userId,
            stationName
          })
        });
        console.log('✅ Added to Favorites Success (API):', response);

        // Handle new backend response format
        if (response && response.success) {
          return response;
        } else {
          throw new Error('Invalid response format from API');
        }
      } catch (apiError) {
        console.log('⚠️ API failed, using mock response:', apiError.message);
        const mockResponse = {
          success: true,
          message: 'İstasyon favorilere eklendi (mock)',
          timestamp: new Date().toISOString()
        };
        console.log('✅ Added to Favorites Success (Mock):', mockResponse);
        return mockResponse;
      }
    } catch (error) {
      console.error('❌ Add to Favorites Failed:', error.message);
      throw error;
    }
  },

  // 5. Remove station from favorites
  removeFromFavorites: async (stationId, userId = 'default-user') => {
    console.log('💔 Removing from Favorites:', { stationId, userId });
    try {
      try {
        const response = await apiRequest('/api/stations/unfavorite', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            stationId,
            userId
          })
        });
        console.log('✅ Removed from Favorites Success (API):', response);

        // Handle new backend response format
        if (response && response.success) {
          return response;
        } else {
          throw new Error('Invalid response format from API');
        }
      } catch (apiError) {
        console.log('⚠️ API failed, using mock response:', apiError.message);
        const mockResponse = {
          success: true,
          message: 'İstasyon favorilerden çıkarıldı (mock)',
          timestamp: new Date().toISOString()
        };
        console.log('✅ Removed from Favorites Success (Mock):', mockResponse);
        return mockResponse;
      }
    } catch (error) {
      console.error('❌ Remove from Favorites Failed:', error.message);
      throw error;
    }
  },

  // 6. Advanced search with filters
  advancedSearch: async (filters) => {
    console.log('🔍 Advanced Search with filters:', filters);
    try {
      const response = await apiRequest('/api/stations/advanced-search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(filters)
      });
      console.log('🎵 Advanced Search API Response:', response);

      // Handle new backend response format
      if (response && response.success && response.data) {
        const transformedStations = response.data.map(transformStationData);
        console.log('🎵 Advanced Search Success:', transformedStations.length, 'stations found');
        return transformedStations;
      } else {
        throw new Error('Invalid response format from API');
      }
    } catch (error) {
      console.error('❌ Advanced Search Failed:', error.message);
      throw error;
    }
  },

  // 7. Record station listen
  recordListen: async (stationId, userId = 'default-user') => {
    console.log('🎧 Recording Listen:', { stationId, userId });
    try {
      try {
        const response = await apiRequest('/api/stations/listen', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            stationId,
            userId
          })
        });
        console.log('✅ Listen Recorded Success (API):', response);

        // Handle new backend response format
        if (response && response.success) {
          return response;
        } else {
          throw new Error('Invalid response format from API');
        }
      } catch (apiError) {
        console.log('⚠️ API failed, using mock response:', apiError.message);
        const mockResponse = {
          success: true,
          message: 'Dinleme kaydedildi (mock)',
          timestamp: new Date().toISOString()
        };
        console.log('✅ Listen Recorded Success (Mock):', mockResponse);
        return mockResponse;
      }
    } catch (error) {
      console.error('❌ Record Listen Failed:', error.message);
      throw error;
    }
  },

  // 8. Vote for station
  voteStation: async (stationId, userId = 'default-user', rating) => {
    console.log('⭐ Voting for Station:', { stationId, userId, rating });
    try {
      try {
        const response = await apiRequest('/api/stations/vote', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            stationId,
            userId,
            rating
          })
        });
        console.log('✅ Vote Success (API):', response);

        // Handle new backend response format
        if (response && response.success) {
          return response;
        } else {
          throw new Error('Invalid response format from API');
        }
      } catch (apiError) {
        console.log('⚠️ API failed, using mock response:', apiError.message);
        const mockResponse = {
          success: true,
          message: `Oy kaydedildi (mock): ${rating} yıldız`,
          timestamp: new Date().toISOString()
        };
        console.log('✅ Vote Success (Mock):', mockResponse);
        return mockResponse;
      }
    } catch (error) {
      console.error('❌ Vote Failed:', error.message);
      throw error;
    }
  },

  // 9. Get user favorites
  getUserFavorites: async (userId = 'default-user') => {
    console.log('❤️ Getting User Favorites for:', userId);
    try {
      try {
        const response = await apiRequest('/api/user/favorites', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ userId })
        });
        console.log('✅ User Favorites API Response:', response);

        // Handle new backend response format
        if (response && response.success && response.data) {
          const transformedStations = response.data.map(transformStationData);
          console.log('✅ User Favorites Success (API):', transformedStations.length, 'favorites found');
          return transformedStations;
        } else {
          throw new Error('Invalid response format from API');
        }
      } catch (apiError) {
        console.log('⚠️ API failed, using mock favorites:', apiError.message);
        const mockFavorites = mockStations.slice(0, 2).map(transformStationData); // Return first 2 as favorites
        console.log('✅ User Favorites Success (Mock):', mockFavorites.length, 'favorites found');
        return mockFavorites;
      }
    } catch (error) {
      console.error('❌ Get User Favorites Failed:', error.message);
      throw error;
    }
  }
};

export default radioApi;
