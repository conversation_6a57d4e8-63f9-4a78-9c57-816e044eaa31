// Backend API base URL
const BASE_URL = 'http://localhost:3000';

// Mock data for testing
const mockStations = [
  {
    id: '1',
    stationuuid: '1',
    name: 'BBC Radio 1',
    country: 'United Kingdom',
    url: 'http://stream.live.vc.bbcmedia.co.uk/bbc_radio_one',
    favicon: 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f1/BBC_Radio_1_logo.svg/200px-BBC_Radio_1_logo.svg.png',
    tags: 'pop,music,uk',
    votes: 1250,
    clickcount: 5000
  },
  {
    id: '2',
    stationuuid: '2',
    name: 'TRT Radyo 1',
    country: 'Turkey',
    url: 'https://radio-trtradyo1.live.trt.com.tr/master_720.m3u8',
    favicon: 'https://upload.wikimedia.org/wikipedia/tr/thumb/c/c3/TRT_Radyo_1_logo.svg/200px-TRT_Radyo_1_logo.svg.png',
    tags: 'news,talk,turkish',
    votes: 890,
    clickcount: 3200
  },
  {
    id: '3',
    stationuuid: '3',
    name: 'NPR News',
    country: 'United States',
    url: 'https://npr-ice.streamguys1.com/live.mp3',
    favicon: 'https://upload.wikimedia.org/wikipedia/commons/thumb/7/79/NPR_logo_2019.svg/200px-NPR_logo_2019.svg.png',
    tags: 'news,talk,usa',
    votes: 2100,
    clickcount: 8500
  },
  {
    id: '4',
    stationuuid: '4',
    name: 'France Inter',
    country: 'France',
    url: 'https://direct.franceinter.fr/live/franceinter-midfi.mp3',
    favicon: 'https://upload.wikimedia.org/wikipedia/commons/thumb/8/8a/France_Inter_logo_2021.svg/200px-France_Inter_logo_2021.svg.png',
    tags: 'news,talk,french',
    votes: 1650,
    clickcount: 4200
  },
  {
    id: '5',
    stationuuid: '5',
    name: 'Radio Deejay',
    country: 'Italy',
    url: 'https://deejay-ice.cast.addradio.de/deejay/mp3/high',
    favicon: 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/5a/Radio_Deejay_logo.svg/200px-Radio_Deejay_logo.svg.png',
    tags: 'music,pop,italian',
    votes: 980,
    clickcount: 2800
  }
];

// Fetch wrapper function with detailed logging
const apiRequest = async (endpoint, options = {}) => {
  const url = `${BASE_URL}${endpoint}`;
  const config = {
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': 'RadioBrowserApp/1.0.0',
    },
    ...options,
  };

  console.log('🔗 API Request:', {
    url,
    method: config.method || 'GET',
    timestamp: new Date().toISOString()
  });

  try {
    const startTime = Date.now();
    const response = await fetch(url, config);
    const endTime = Date.now();

    console.log('📡 API Response:', {
      url,
      status: response.status,
      statusText: response.statusText,
      duration: `${endTime - startTime}ms`,
      timestamp: new Date().toISOString()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
    }

    const data = await response.json();
    console.log('✅ API Data received:', {
      url,
      dataLength: Array.isArray(data) ? data.length : 'object',
      firstItem: Array.isArray(data) && data.length > 0 ? data[0] : null,
      timestamp: new Date().toISOString()
    });

    return data;
  } catch (error) {
    console.error('❌ API Request Error:', {
      url,
      error: error.message,
      timestamp: new Date().toISOString()
    });
    throw error;
  }
};

// Backend API Functions - GET & POST Endpoints
export const radioApi = {

  // GET Endpoints

  // 1. Get top/popular stations
  getTopStations: async (limit = 10) => {
    console.log('🎯 Getting Top Stations...');
    try {
      // Try real API first, fallback to mock data
      try {
        const data = await apiRequest(`/api/stations/top?limit=${limit}`);
        console.log('🎵 Top Stations Success (API):', data?.length || 0, 'stations received');
        return data;
      } catch (apiError) {
        console.log('⚠️ API failed, using mock data:', apiError.message);
        const mockData = mockStations.slice(0, limit);
        console.log('🎵 Top Stations Success (Mock):', mockData.length, 'stations received');
        return mockData;
      }
    } catch (error) {
      console.error('❌ Top Stations Failed:', error.message);
      throw error;
    }
  },

  // 2. Search stations by name
  searchStations: async (query, limit = 10) => {
    console.log('🔍 Searching Stations for:', query);
    try {
      // Try real API first, fallback to mock data
      try {
        const data = await apiRequest(`/api/stations/search/${encodeURIComponent(query)}?limit=${limit}`);
        console.log('🎵 Search Success (API):', data?.length || 0, 'stations found for:', query);
        return data;
      } catch (apiError) {
        console.log('⚠️ API failed, using mock search:', apiError.message);
        const mockData = mockStations.filter(station =>
          station.name.toLowerCase().includes(query.toLowerCase()) ||
          station.tags.toLowerCase().includes(query.toLowerCase())
        ).slice(0, limit);
        console.log('🎵 Search Success (Mock):', mockData.length, 'stations found for:', query);
        return mockData;
      }
    } catch (error) {
      console.error('❌ Search Failed for:', query, error.message);
      throw error;
    }
  },

  // 3. Get stations by country
  getStationsByCountry: async (country, limit = 10) => {
    console.log('🌍 Getting Stations for Country:', country);
    try {
      // Try real API first, fallback to mock data
      try {
        const data = await apiRequest(`/api/stations/country/${encodeURIComponent(country)}?limit=${limit}`);
        console.log('🎵 Country Stations Success (API):', data?.length || 0, 'stations for:', country);
        return data;
      } catch (apiError) {
        console.log('⚠️ API failed, using mock country data:', apiError.message);
        const mockData = mockStations.filter(station =>
          station.country.toLowerCase().includes(country.toLowerCase())
        ).slice(0, limit);
        console.log('🎵 Country Stations Success (Mock):', mockData.length, 'stations for:', country);
        return mockData;
      }
    } catch (error) {
      console.error('❌ Country Stations Failed for:', country, error.message);
      throw error;
    }
  },

  // POST Endpoints

  // 4. Add station to favorites
  addToFavorites: async (stationId, userId, stationName) => {
    console.log('❤️ Adding to Favorites:', { stationId, userId, stationName });
    try {
      // Try real API first, fallback to mock response
      try {
        const data = await apiRequest('/api/stations/favorite', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            stationId,
            userId,
            stationName
          })
        });
        console.log('✅ Added to Favorites Success (API):', data);
        return data;
      } catch (apiError) {
        console.log('⚠️ API failed, using mock response:', apiError.message);
        const mockResponse = { success: true, message: 'Added to favorites (mock)' };
        console.log('✅ Added to Favorites Success (Mock):', mockResponse);
        return mockResponse;
      }
    } catch (error) {
      console.error('❌ Add to Favorites Failed:', error.message);
      throw error;
    }
  },

  // 5. Remove station from favorites
  removeFromFavorites: async (stationId, userId) => {
    console.log('💔 Removing from Favorites:', { stationId, userId });
    try {
      try {
        const data = await apiRequest('/api/stations/unfavorite', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            stationId,
            userId
          })
        });
        console.log('✅ Removed from Favorites Success (API):', data);
        return data;
      } catch (apiError) {
        console.log('⚠️ API failed, using mock response:', apiError.message);
        const mockResponse = { success: true, message: 'Removed from favorites (mock)' };
        console.log('✅ Removed from Favorites Success (Mock):', mockResponse);
        return mockResponse;
      }
    } catch (error) {
      console.error('❌ Remove from Favorites Failed:', error.message);
      throw error;
    }
  },

  // 6. Advanced search with filters
  advancedSearch: async (filters) => {
    console.log('🔍 Advanced Search with filters:', filters);
    try {
      const data = await apiRequest('/api/stations/advanced-search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(filters)
      });
      console.log('🎵 Advanced Search Success:', data?.length || 0, 'stations found');
      return data;
    } catch (error) {
      console.error('❌ Advanced Search Failed:', error.message);
      throw error;
    }
  },

  // 7. Record station listen
  recordListen: async (stationId, userId) => {
    console.log('🎧 Recording Listen:', { stationId, userId });
    try {
      try {
        const data = await apiRequest('/api/stations/listen', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            stationId,
            userId
          })
        });
        console.log('✅ Listen Recorded Success (API):', data);
        return data;
      } catch (apiError) {
        console.log('⚠️ API failed, using mock response:', apiError.message);
        const mockResponse = { success: true, message: 'Listen recorded (mock)' };
        console.log('✅ Listen Recorded Success (Mock):', mockResponse);
        return mockResponse;
      }
    } catch (error) {
      console.error('❌ Record Listen Failed:', error.message);
      throw error;
    }
  },

  // 8. Vote for station
  voteStation: async (stationId, userId, rating) => {
    console.log('⭐ Voting for Station:', { stationId, userId, rating });
    try {
      try {
        const data = await apiRequest('/api/stations/vote', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            stationId,
            userId,
            rating
          })
        });
        console.log('✅ Vote Success (API):', data);
        return data;
      } catch (apiError) {
        console.log('⚠️ API failed, using mock response:', apiError.message);
        const mockResponse = { success: true, message: `Vote recorded (mock): ${rating} stars` };
        console.log('✅ Vote Success (Mock):', mockResponse);
        return mockResponse;
      }
    } catch (error) {
      console.error('❌ Vote Failed:', error.message);
      throw error;
    }
  },

  // 9. Get user favorites
  getUserFavorites: async (userId) => {
    console.log('❤️ Getting User Favorites for:', userId);
    try {
      try {
        const data = await apiRequest('/api/user/favorites', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ userId })
        });
        console.log('✅ User Favorites Success (API):', data?.length || 0, 'favorites found');
        return data;
      } catch (apiError) {
        console.log('⚠️ API failed, using mock favorites:', apiError.message);
        const mockFavorites = mockStations.slice(0, 2); // Return first 2 as favorites
        console.log('✅ User Favorites Success (Mock):', mockFavorites.length, 'favorites found');
        return mockFavorites;
      }
    } catch (error) {
      console.error('❌ Get User Favorites Failed:', error.message);
      throw error;
    }
  }
};

export default radioApi;
