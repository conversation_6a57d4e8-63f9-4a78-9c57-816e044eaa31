import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Alert,
  Image,
  ScrollView,
  Linking,
  Share,
} from 'react-native';
import { radioApi } from '../services/radioApi';

export default function StationDetailScreen({ navigation, route }) {
  const { station } = route.params;
  const [isPlaying, setIsPlaying] = useState(false);
  const [isFavorite, setIsFavorite] = useState(false);
  const [userRating, setUserRating] = useState(0);

  // Mock user ID - in real app this would come from authentication
  const userId = 'user123';

  useEffect(() => {
    navigation.setOptions({ title: station.name });
    console.log('📻 Station Detail opened for:', station.name);
  }, []);

  const handlePlay = async () => {
    try {
      console.log('▶️ Playing station:', station.name);

      // Record listen for statistics
      await radioApi.recordListen(station.id || station.stationuuid, userId);

      // Open the stream URL
      if (station.streamUrl || station.url_resolved || station.url) {
        const streamUrl = station.streamUrl || station.url_resolved || station.url;
        const supported = await Linking.canOpenURL(streamUrl);

        if (supported) {
          await Linking.openURL(streamUrl);
          setIsPlaying(true);
          console.log('✅ Station opened successfully');
        } else {
          Alert.alert('Hata', 'Bu istasyon açılamıyor');
        }
      } else {
        Alert.alert('Hata', 'İstasyon URL\'si bulunamadı');
      }
    } catch (error) {
      console.error('❌ Play error:', error);
      Alert.alert('Hata', 'İstasyon açılırken bir hata oluştu: ' + error.message);
    }
  };

  const handleVote = async (rating = 5) => {
    try {
      console.log('⭐ Voting for station with rating:', rating);
      await radioApi.voteStation(station.id || station.stationuuid, userId, rating);
      setUserRating(rating);
      Alert.alert('Teşekkürler', `${rating} yıldız oyunuz kaydedildi!`);
    } catch (error) {
      console.error('❌ Vote error:', error);
      Alert.alert('Hata', 'Oy verirken bir hata oluştu: ' + error.message);
    }
  };

  const handleFavorite = async () => {
    try {
      const newFavoriteState = !isFavorite;
      console.log('❤️ Toggling favorite state to:', newFavoriteState);

      if (newFavoriteState) {
        await radioApi.addToFavorites(
          station.id || station.stationuuid,
          userId,
          station.name
        );
        Alert.alert('✅ Favorilere Eklendi', station.name);
      } else {
        await radioApi.removeFromFavorites(
          station.id || station.stationuuid,
          userId
        );
        Alert.alert('💔 Favorilerden Çıkarıldı', station.name);
      }

      setIsFavorite(newFavoriteState);
    } catch (error) {
      console.error('❌ Favorite error:', error);
      Alert.alert('Hata', 'Favori işlemi sırasında bir hata oluştu: ' + error.message);
    }
  };

  const handleShare = async () => {
    try {
      await Share.share({
        message: `${station.name} radyo istasyonunu dinle: ${station.homepage || station.url}`,
        title: station.name,
      });
    } catch (error) {
      console.error('Share error:', error);
    }
  };

  const openHomepage = async () => {
    if (station.homepage) {
      const supported = await Linking.canOpenURL(station.homepage);
      if (supported) {
        await Linking.openURL(station.homepage);
      }
    }
  };

  return (
    <ScrollView style={styles.container}>
      {/* Station Header */}
      <View style={styles.header}>
        <Image
          source={{ uri: station.favicon || 'https://via.placeholder.com/100' }}
          style={styles.stationImage}
          defaultSource={{ uri: 'https://via.placeholder.com/100' }}
        />
        <Text style={styles.stationName}>{station.name}</Text>
        <Text style={styles.stationCountry}>{station.country}</Text>
      </View>

      {/* Play Button */}
      <TouchableOpacity style={styles.playButton} onPress={handlePlay}>
        <Text style={styles.playButtonText}>
          {isPlaying ? '⏸️ Durdur' : '▶️ Dinle'}
        </Text>
      </TouchableOpacity>

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <TouchableOpacity style={styles.actionButton} onPress={handleFavorite}>
          <Text style={styles.actionButtonText}>
            {isFavorite ? '💔' : '❤️'}
          </Text>
          <Text style={styles.actionButtonLabel}>Favori</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton} onPress={handleVote}>
          <Text style={styles.actionButtonText}>👍</Text>
          <Text style={styles.actionButtonLabel}>Oy Ver</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton} onPress={handleShare}>
          <Text style={styles.actionButtonText}>📤</Text>
          <Text style={styles.actionButtonLabel}>Paylaş</Text>
        </TouchableOpacity>
      </View>

      {/* Station Info */}
      <View style={styles.infoSection}>
        <Text style={styles.sectionTitle}>İstasyon Bilgileri</Text>

        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Ülke:</Text>
          <Text style={styles.infoValue}>{station.country || 'Bilinmiyor'}</Text>
        </View>

        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Dil:</Text>
          <Text style={styles.infoValue}>{station.language || 'Bilinmiyor'}</Text>
        </View>

        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Türler:</Text>
          <Text style={styles.infoValue}>{station.tags || 'Belirtilmemiş'}</Text>
        </View>

        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Codec:</Text>
          <Text style={styles.infoValue}>{station.codec || 'Bilinmiyor'}</Text>
        </View>

        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Bitrate:</Text>
          <Text style={styles.infoValue}>{station.bitrate ? `${station.bitrate} kbps` : 'Bilinmiyor'}</Text>
        </View>

        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Oylar:</Text>
          <Text style={styles.infoValue}>{station.votes || 0}</Text>
        </View>

        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Dinlenme:</Text>
          <Text style={styles.infoValue}>{station.clickcount || 0}</Text>
        </View>

        {station.homepage && (
          <TouchableOpacity style={styles.websiteButton} onPress={openHomepage}>
            <Text style={styles.websiteButtonText}>🌐 Web Sitesi</Text>
          </TouchableOpacity>
        )}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
    marginBottom: 16,
  },
  stationImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: 16,
  },
  stationName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 8,
  },
  stationCountry: {
    fontSize: 16,
    color: '#666',
  },
  playButton: {
    backgroundColor: '#007AFF',
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 16,
    borderRadius: 10,
    alignItems: 'center',
  },
  playButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  actionButtons: {
    flexDirection: 'row',
    marginHorizontal: 16,
    marginBottom: 16,
    gap: 10,
  },
  actionButton: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 12,
    borderRadius: 10,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  actionButtonText: {
    fontSize: 24,
    marginBottom: 4,
  },
  actionButtonLabel: {
    fontSize: 12,
    color: '#666',
  },
  infoSection: {
    backgroundColor: '#fff',
    margin: 16,
    padding: 16,
    borderRadius: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 14,
    color: '#666',
    fontWeight: '600',
  },
  infoValue: {
    fontSize: 14,
    color: '#333',
    flex: 1,
    textAlign: 'right',
  },
  websiteButton: {
    backgroundColor: '#007AFF',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
  },
  websiteButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});
