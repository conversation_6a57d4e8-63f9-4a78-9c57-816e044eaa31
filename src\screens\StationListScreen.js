import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Image,
  RefreshControl,
} from 'react-native';
import { radioApi } from '../services/radioApi';

export default function StationListScreen({ navigation, route }) {
  const { type, value, title } = route.params;
  const [stations, setStations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    navigation.setOptions({ title });
    loadStations();
  }, []);

  const loadStations = async () => {
    console.log('📡 Loading stations for:', type, value);
    try {
      setLoading(true);
      console.log('⏳ Station loading state set to true');

      let stationsData;

      if (type === 'country') {
        console.log('🌍 Loading stations by country:', value);
        stationsData = await radioApi.getStationsByCountry(value, 10);
      } else {
        console.log('⚠️ Unsupported type:', type);
        stationsData = [];
      }

      console.log('✅ Stations loaded:', stationsData?.length || 0);
      setStations(stationsData || []);
      console.log('💾 Stations saved to state');
    } catch (error) {
      console.error('❌ Failed to load stations:', error);
      Alert.alert('Hata', 'İstasyonlar yüklenirken bir hata oluştu: ' + error.message);
    } finally {
      setLoading(false);
      console.log('✅ Station loading completed');
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadStations();
    setRefreshing(false);
  };

  const handleStationPress = (station) => {
    navigation.navigate('StationDetail', { station });
  };

  const renderStation = ({ item }) => (
    <TouchableOpacity
      style={styles.stationCard}
      onPress={() => handleStationPress(item)}
    >
      <View style={styles.stationInfo}>
        <Image
          source={{ uri: item.favicon || 'https://via.placeholder.com/50' }}
          style={styles.stationImage}
          defaultSource={{ uri: 'https://via.placeholder.com/50' }}
        />
        <View style={styles.stationDetails}>
          <Text style={styles.stationName} numberOfLines={1}>
            {item.name}
          </Text>
          <Text style={styles.stationCountry} numberOfLines={1}>
            {item.country} • {item.tags}
          </Text>
          <Text style={styles.stationVotes}>
            ❤️ {item.votes} • 👥 {item.clickcount}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyText}>Bu kategoride istasyon bulunamadı</Text>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>İstasyonlar yükleniyor...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={stations}
        renderItem={renderStation}
        keyExtractor={(item) => item.id || item.stationuuid}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={renderEmptyState}
        contentContainerStyle={stations.length === 0 ? styles.emptyList : null}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  stationCard: {
    backgroundColor: '#fff',
    borderRadius: 10,
    marginBottom: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  stationInfo: {
    flexDirection: 'row',
    padding: 12,
    alignItems: 'center',
  },
  stationImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  stationDetails: {
    flex: 1,
  },
  stationName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  stationCountry: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  stationVotes: {
    fontSize: 12,
    color: '#999',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  emptyList: {
    flex: 1,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
  },
});
